use crate::types::{Config, ModelDefinition, FieldType};
use std::fs;

/// Resolves file paths for both traditional Laravel and DDD structures
pub struct PathResolver;

impl PathResolver {
    /// Get the file path for a model component
    pub fn get_model_path(model: &ModelDefinition, config: &Config) -> String {
        if config.use_ddd_structure {
            format!("{}/app/Domain/{}/Models/{}.php", config.output_dir, model.name, model.name)
        } else {
            format!("{}/app/Models/{}.php", config.output_dir, model.name)
        }
    }

    /// Get the file path for a controller (always in traditional Laravel structure)
    pub fn get_controller_path(model: &ModelDefinition, config: &Config) -> String {
        format!("{}/app/Http/Controllers/{}Controller.php", config.output_dir, model.name)
    }

    /// Get the file path for a resource
    pub fn get_resource_path(model: &ModelDefinition, config: &Config) -> String {
        if config.use_ddd_structure {
            format!("{}/app/Domain/{}/Resources/{}Resource.php", config.output_dir, model.name, model.name)
        } else {
            format!("{}/app/Http/Resources/{}Resource.php", config.output_dir, model.name)
        }
    }

    /// Get the file path for a factory
    pub fn get_factory_path(model: &ModelDefinition, config: &Config) -> String {
        if config.use_ddd_structure {
            format!("{}/app/Domain/{}/Factories/{}Factory.php", config.output_dir, model.name, model.name)
        } else {
            format!("{}/database/factories/{}Factory.php", config.output_dir, model.name)
        }
    }

    /// Get the file path for a DTO
    pub fn get_dto_path(model: &ModelDefinition, config: &Config) -> String {
        if config.use_ddd_structure {
            format!("{}/app/Domain/{}/DTOs/{}DTO.php", config.output_dir, model.name, model.name)
        } else {
            format!("{}/app/DTOs/{}DTO.php", config.output_dir, model.name)
        }
    }

    /// Get the file path for a migration (always in traditional Laravel structure)
    pub fn get_migration_path(model: &ModelDefinition, config: &Config) -> String {
        let timestamp = chrono::Utc::now().format("%Y_%m_%d_%H%M%S");
        format!("{}/database/migrations/{}_create_{}_table.php", 
                config.output_dir, timestamp, model.table)
    }
}

/// Resolves namespaces for both traditional Laravel and DDD structures
pub struct NamespaceResolver;

impl NamespaceResolver {
    /// Get the namespace for a model
    pub fn get_model_namespace(model: &ModelDefinition, config: &Config) -> String {
        if config.use_ddd_structure {
            format!("App\\Domain\\{}\\Models", model.name)
        } else {
            config.namespace.clone()
        }
    }

    /// Get the namespace for a controller (always in traditional Laravel structure)
    pub fn get_controller_namespace(_model: &ModelDefinition, _config: &Config) -> String {
        "App\\Http\\Controllers".to_string()
    }

    /// Get the namespace for a resource
    pub fn get_resource_namespace(model: &ModelDefinition, config: &Config) -> String {
        if config.use_ddd_structure {
            format!("App\\Domain\\{}\\Resources", model.name)
        } else {
            "App\\Http\\Resources".to_string()
        }
    }

    /// Get the namespace for a factory
    pub fn get_factory_namespace(model: &ModelDefinition, config: &Config) -> String {
        if config.use_ddd_structure {
            format!("App\\Domain\\{}\\Factories", model.name)
        } else {
            "Database\\Factories".to_string()
        }
    }

    /// Get the namespace for a DTO
    pub fn get_dto_namespace(model: &ModelDefinition, config: &Config) -> String {
        if config.use_ddd_structure {
            format!("App\\Domain\\{}\\DTOs", model.name)
        } else {
            "App\\DTOs".to_string()
        }
    }
}

/// Creates directories for both traditional Laravel and DDD structures
pub struct DirectoryCreator;

impl DirectoryCreator {
    /// Create all necessary directories for a model
    pub fn create_model_directories(model: &ModelDefinition, config: &Config) -> crate::error::Result<()> {
        if config.use_ddd_structure {
            let base_domain_dir = format!("{}/app/Domain/{}", config.output_dir, model.name);
            
            let dirs = [
                &base_domain_dir,
                &format!("{}/Models", base_domain_dir),
                &format!("{}/Resources", base_domain_dir),
                &format!("{}/Factories", base_domain_dir),
                &format!("{}/DTOs", base_domain_dir),
            ];

            for dir in dirs {
                fs::create_dir_all(dir)?;
            }
        } else {
            let dirs = [
                &format!("{}/app/Models", config.output_dir),
                &format!("{}/app/Http/Resources", config.output_dir),
                &format!("{}/database/factories", config.output_dir),
                &format!("{}/app/DTOs", config.output_dir),
            ];

            for dir in dirs {
                fs::create_dir_all(dir)?;
            }
        }

        // Always create these directories (they don't change with DDD)
        let common_dirs = [
            &format!("{}/app/Http/Controllers", config.output_dir),
            &format!("{}/database/migrations", config.output_dir),
        ];

        for dir in common_dirs {
            fs::create_dir_all(dir)?;
        }

        Ok(())
    }
}

/// Utility functions for field type handling
pub struct FieldTypeHelper;

impl FieldTypeHelper {
    /// Convert field type to PHP type hint
    pub fn to_php_type_hint(field_type: &FieldType) -> &'static str {
        match field_type {
            FieldType::String | FieldType::Text | FieldType::LongText | FieldType::MediumText => "string",
            FieldType::Integer | FieldType::BigInteger | FieldType::TinyInteger | 
            FieldType::SmallInteger | FieldType::MediumInteger => "int",
            FieldType::Float | FieldType::Decimal => "float",
            FieldType::Boolean => "bool",
            FieldType::Json => "array",
            FieldType::Date | FieldType::DateTime | FieldType::Timestamp => "string",
            FieldType::Uuid => "string",
            FieldType::Enum => "string",
            FieldType::Binary => "string",
            FieldType::Inet => "string",
        }
    }

    /// Check if field should be nullable in PHP
    pub fn is_nullable_in_php(field_name: &str, nullable: bool) -> bool {
        // ID fields are never nullable in PHP constructors
        if field_name == "id" {
            return false;
        }
        nullable
    }
}
