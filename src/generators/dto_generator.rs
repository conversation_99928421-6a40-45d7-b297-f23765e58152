use crate::generators::Generator;
use crate::types::{Config, ModelDefinition};

pub struct DtoGenerator;

const TEMPLATE: &str = include_str!("../templates/dto.php.template");

impl Generator for DtoGenerator {
    fn generate(&self, model: &ModelDefinition, config: &Config) -> crate::error::Result<String> {
        let table_name = &model.table;




    }

    fn get_file_path(&self, model: &ModelDefinition, config: &Config) -> String {
        todo!()
    }
}