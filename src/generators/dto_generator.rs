use crate::generators::Generator;
use crate::generators::shared::{PathResolver, NamespaceResolver, FieldTypeHelper};
use crate::types::{Config, ModelDefinition};

pub struct DtoGenerator;

const TEMPLATE: &str = include_str!("../templates/dto.php.template");

impl Generator for DtoGenerator {
    fn generate(&self, model: &ModelDefinition, config: &Config) -> crate::error::Result<String> {
        let mut content = TEMPLATE.to_string();

        // Replace namespace
        let namespace = NamespaceResolver::get_dto_namespace(model, config);
        content = content.replace("{{ namespace }}", &format!("namespace {};", namespace));

        // Replace DTO name
        content = content.replace("{{dto_name}}", &model.name);

        // Generate constructor fields
        let constructor_fields = self.generate_constructor_fields(model);

        // Generate fromArray fields
        let from_array_fields = self.generate_from_array_fields(model);

        // Generate toArray fields
        let to_array_fields = self.generate_to_array_fields(model);

        // Replace all resource_fields placeholders
        // We need to handle each occurrence separately since they have different content
        content = self.replace_resource_fields_placeholders(content, &constructor_fields, &from_array_fields, &to_array_fields);

        Ok(content)
    }

    fn get_file_path(&self, model: &ModelDefinition, config: &Config) -> String {
        PathResolver::get_dto_path(model, config)
    }
}

impl DtoGenerator {
    fn generate_constructor_fields(&self, model: &ModelDefinition) -> String {
        let mut fields = Vec::new();

        // Always include ID first
        fields.push("public int $id".to_string());

        // Add all other fields
        for field in &model.fields {
            if field.name != "id" {
                let php_type = FieldTypeHelper::to_php_type_hint(&field.field_type);
                let nullable = if FieldTypeHelper::is_nullable_in_php(&field.name, field.nullable) {
                    "?"
                } else {
                    ""
                };
                fields.push(format!("public {}{} ${}", nullable, php_type, field.name));
            }
        }

        // Add timestamps if enabled
        if model.timestamps {
            fields.push("public ?string $created_at".to_string());
            fields.push("public ?string $updated_at".to_string());
        }

        // Add soft delete timestamp if enabled
        if model.soft_deletes {
            fields.push("public ?string $deleted_at".to_string());
        }

        fields.join(",\n        ")
    }

    fn generate_from_array_fields(&self, model: &ModelDefinition) -> String {
        let mut fields = Vec::new();

        // Always include ID first
        fields.push("$data['id']".to_string());

        // Add all other fields
        for field in &model.fields {
            if field.name != "id" {
                fields.push(format!("$data['{}']", field.name));
            }
        }

        // Add timestamps if enabled
        if model.timestamps {
            fields.push("$data['created_at']".to_string());
            fields.push("$data['updated_at']".to_string());
        }

        // Add soft delete timestamp if enabled
        if model.soft_deletes {
            fields.push("$data['deleted_at']".to_string());
        }

        fields.join(",\n                ")
    }

    fn generate_to_array_fields(&self, model: &ModelDefinition) -> String {
        let mut fields = Vec::new();

        // Always include ID first
        fields.push("'id' => $this->id".to_string());

        // Add all other fields
        for field in &model.fields {
            if field.name != "id" {
                fields.push(format!("'{}' => $this->{}", field.name, field.name));
            }
        }

        // Add timestamps if enabled
        if model.timestamps {
            fields.push("'created_at' => $this->created_at".to_string());
            fields.push("'updated_at' => $this->updated_at".to_string());
        }

        // Add soft delete timestamp if enabled
        if model.soft_deletes {
            fields.push("'deleted_at' => $this->deleted_at".to_string());
        }

        fields.join(",\n            ")
    }

    fn replace_resource_fields_placeholders(&self, mut content: String, constructor_fields: &str, from_array_fields: &str, to_array_fields: &str) -> String {
        // The template has {{resource_fields}} in three different contexts
        // We need to replace them in order: constructor, fromArray, toArray

        // First occurrence: constructor parameters
        if let Some(start) = content.find("{{resource_fields}}") {
            content.replace_range(start..start + "{{resource_fields}}".len(), constructor_fields);
        }

        // Second occurrence: fromArray method
        if let Some(start) = content.find("{{resource_fields}}") {
            content.replace_range(start..start + "{{resource_fields}}".len(), from_array_fields);
        }

        // Third occurrence: toArray method
        if let Some(start) = content.find("{{resource_fields}}") {
            content.replace_range(start..start + "{{resource_fields}}".len(), to_array_fields);
        }

        content
    }
}