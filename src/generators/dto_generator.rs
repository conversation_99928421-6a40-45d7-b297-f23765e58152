use crate::generators::Generator;
use crate::generators::shared::{<PERSON><PERSON><PERSON><PERSON><PERSON>, Namespace<PERSON><PERSON><PERSON><PERSON>, FieldTypeHelper};
use crate::types::{Config, ModelDefinition};
use crate::validation::Validator;
use crate::error::GeneratorError;
use crate::template::{Template<PERSON>ontext, Template<PERSON><PERSON><PERSON>};

pub struct DtoGenerator;

const TEMPLATE: &str = include_str!("../templates/dto.php.template");

impl Generator for DtoGenerator {
    fn generate(&self, model: &ModelDefinition, config: &Config) -> crate::error::Result<String> {
        // Validate the model before generating
        Validator::validate_model(model)?;

        // Validate configuration
        if config.output_dir.is_empty() {
            return Err(GeneratorError::Configuration(
                "Output directory cannot be empty".to_string()
            ));
        }

        // Validate DTO class name
        Validator::validate_identifier(&model.name, "DTO class name")?;

        // Create template context
        let namespace = NamespaceResolver::get_dto_namespace(model, config);
        let constructor_fields = self.generate_constructor_fields(model)?;
        let from_array_fields = self.generate_from_array_fields(model)?;
        let to_array_fields = self.generate_to_array_fields(model)?;

        let context = TemplateContext::new()
            .with("namespace", format!("namespace {};", namespace))
            .with("dto_name", &model.name)
            .with("constructor_fields", constructor_fields)
            .with("from_array_fields", from_array_fields)
            .with("to_array_fields", to_array_fields);

        // Render template with context
        let content = TemplateRenderer::render_with_required_vars(
            TEMPLATE,
            &context,
            &["namespace", "dto_name", "constructor_fields", "from_array_fields", "to_array_fields"]
        )?;

        Ok(content)
    }

    fn get_file_path(&self, model: &ModelDefinition, config: &Config) -> String {
        PathResolver::get_dto_path(model, config)
    }
}

impl DtoGenerator {
    fn generate_constructor_fields(&self, model: &ModelDefinition) -> crate::error::Result<String> {
        let mut fields = Vec::new();

        // Always include ID first
        fields.push("public int $id".to_string());

        // Add all other fields
        for field in &model.fields {
            if field.name != "id" {
                // Validate field name
                Validator::validate_identifier(&field.name, "Field name")?;

                let php_type = FieldTypeHelper::to_php_type_hint(&field.field_type);
                let nullable = if FieldTypeHelper::is_nullable_in_php(&field.name, field.nullable) {
                    "?"
                } else {
                    ""
                };
                fields.push(format!("public {}{} ${}", nullable, php_type, field.name));
            }
        }

        // Add timestamps if enabled
        if model.timestamps {
            fields.push("public ?string $created_at".to_string());
            fields.push("public ?string $updated_at".to_string());
        }

        // Add soft delete timestamp if enabled
        if model.soft_deletes {
            fields.push("public ?string $deleted_at".to_string());
        }

        Ok(fields.join(",\n        "))
    }

    fn generate_from_array_fields(&self, model: &ModelDefinition) -> crate::error::Result<String> {
        let mut fields = Vec::new();

        // Always include ID first
        fields.push("$data['id']".to_string());

        // Add all other fields
        for field in &model.fields {
            if field.name != "id" {
                // Validate field name
                Validator::validate_identifier(&field.name, "Field name")?;
                fields.push(format!("$data['{}']", field.name));
            }
        }

        // Add timestamps if enabled
        if model.timestamps {
            fields.push("$data['created_at']".to_string());
            fields.push("$data['updated_at']".to_string());
        }

        // Add soft delete timestamp if enabled
        if model.soft_deletes {
            fields.push("$data['deleted_at']".to_string());
        }

        Ok(fields.join(",\n                "))
    }

    fn generate_to_array_fields(&self, model: &ModelDefinition) -> crate::error::Result<String> {
        let mut fields = Vec::new();

        // Always include ID first
        fields.push("'id' => $this->id".to_string());

        // Add all other fields
        for field in &model.fields {
            if field.name != "id" {
                // Validate field name
                Validator::validate_identifier(&field.name, "Field name")?;
                fields.push(format!("'{}' => $this->{}", field.name, field.name));
            }
        }

        // Add timestamps if enabled
        if model.timestamps {
            fields.push("'created_at' => $this->created_at".to_string());
            fields.push("'updated_at' => $this->updated_at".to_string());
        }

        // Add soft delete timestamp if enabled
        if model.soft_deletes {
            fields.push("'deleted_at' => $this->deleted_at".to_string());
        }

        Ok(fields.join(",\n            "))
    }


}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::types::{Field, FieldType, FillableGuarded};

    fn create_test_model() -> ModelDefinition {
        ModelDefinition {
            name: "User".to_string(),
            table: "users".to_string(),
            fields: vec![
                Field {
                    name: "name".to_string(),
                    field_type: FieldType::String,
                    nullable: false,
                    unique: false,
                    default: None,
                    length: Some(255),
                    index: false,
                    enum_values: vec![],
                    decimal_precision: None,
                    unsigned: false,
                    auto_increment: false,
                    primary: false,
                    comment: None,
                    validation_rules: vec![],
                    cast_type: None,
                },
                Field {
                    name: "email".to_string(),
                    field_type: FieldType::String,
                    nullable: false,
                    unique: true,
                    default: None,
                    length: Some(255),
                    index: false,
                    enum_values: vec![],
                    decimal_precision: None,
                    unsigned: false,
                    auto_increment: false,
                    primary: false,
                    comment: None,
                    validation_rules: vec![],
                    cast_type: None,
                },
                Field {
                    name: "age".to_string(),
                    field_type: FieldType::Integer,
                    nullable: true,
                    unique: false,
                    default: None,
                    length: None,
                    index: false,
                    enum_values: vec![],
                    decimal_precision: None,
                    unsigned: false,
                    auto_increment: false,
                    primary: false,
                    comment: None,
                    validation_rules: vec![],
                    cast_type: None,
                },
            ],
            timestamps: true,
            soft_deletes: false,
            relationships: vec![],
            pivot_tables: vec![],
            validation_rules: vec![],
            traits: vec![],
            fillable_guarded: FillableGuarded::All,
        }
    }

    fn create_test_config(use_ddd: bool) -> Config {
        Config {
            models: vec![],
            output_dir: "/tmp/test".to_string(),
            namespace: "App\\Models".to_string(),
            generate_models: true,
            generate_controllers: true,
            generate_resources: true,
            generate_factories: true,
            generate_migrations: true,
            generate_pivot_tables: true,
            generate_validation_rules: true,
            generate_dto: true,
            use_ddd_structure: use_ddd,
            database_engine: "mysql".to_string(),
            force_overwrite: false,
        }
    }

    #[test]
    fn test_dto_generation_traditional_structure() {
        let generator = DtoGenerator;
        let model = create_test_model();
        let config = create_test_config(false);

        let result = generator.generate(&model, &config).unwrap();

        // Check namespace
        assert!(result.contains("namespace App\\DTOs;"));

        // Check class name
        assert!(result.contains("class UserDTO {"));

        // Check constructor fields
        assert!(result.contains("public int $id"));
        assert!(result.contains("public string $name"));
        assert!(result.contains("public string $email"));
        assert!(result.contains("public ?int $age"));
        assert!(result.contains("public ?string $created_at"));
        assert!(result.contains("public ?string $updated_at"));

        // Check fromArray method
        assert!(result.contains("$data['id']"));
        assert!(result.contains("$data['name']"));
        assert!(result.contains("$data['email']"));
        assert!(result.contains("$data['age']"));

        // Check toArray method
        assert!(result.contains("'id' => $this->id"));
        assert!(result.contains("'name' => $this->name"));
        assert!(result.contains("'email' => $this->email"));
        assert!(result.contains("'age' => $this->age"));
    }

    #[test]
    fn test_dto_generation_ddd_structure() {
        let generator = DtoGenerator;
        let model = create_test_model();
        let config = create_test_config(true);

        let result = generator.generate(&model, &config).unwrap();

        // Check DDD namespace
        assert!(result.contains("namespace App\\Domain\\User\\DTOs;"));

        // Check class name
        assert!(result.contains("class UserDTO {"));
    }

    #[test]
    fn test_dto_file_path_traditional() {
        let generator = DtoGenerator;
        let model = create_test_model();
        let config = create_test_config(false);

        let path = generator.get_file_path(&model, &config);
        assert_eq!(path, "/tmp/test/app/DTOs/UserDTO.php");
    }

    #[test]
    fn test_dto_file_path_ddd() {
        let generator = DtoGenerator;
        let model = create_test_model();
        let config = create_test_config(true);

        let path = generator.get_file_path(&model, &config);
        assert_eq!(path, "/tmp/test/app/Domain/User/DTOs/UserDTO.php");
    }

    #[test]
    fn test_constructor_fields_generation() {
        let generator = DtoGenerator;
        let model = create_test_model();

        let result = generator.generate_constructor_fields(&model).unwrap();

        assert!(result.contains("public int $id"));
        assert!(result.contains("public string $name"));
        assert!(result.contains("public string $email"));
        assert!(result.contains("public ?int $age"));
        assert!(result.contains("public ?string $created_at"));
        assert!(result.contains("public ?string $updated_at"));
    }

    #[test]
    fn test_from_array_fields_generation() {
        let generator = DtoGenerator;
        let model = create_test_model();

        let result = generator.generate_from_array_fields(&model).unwrap();

        assert!(result.contains("$data['id']"));
        assert!(result.contains("$data['name']"));
        assert!(result.contains("$data['email']"));
        assert!(result.contains("$data['age']"));
        assert!(result.contains("$data['created_at']"));
        assert!(result.contains("$data['updated_at']"));
    }

    #[test]
    fn test_to_array_fields_generation() {
        let generator = DtoGenerator;
        let model = create_test_model();

        let result = generator.generate_to_array_fields(&model).unwrap();

        assert!(result.contains("'id' => $this->id"));
        assert!(result.contains("'name' => $this->name"));
        assert!(result.contains("'email' => $this->email"));
        assert!(result.contains("'age' => $this->age"));
        assert!(result.contains("'created_at' => $this->created_at"));
        assert!(result.contains("'updated_at' => $this->updated_at"));
    }

    #[test]
    fn test_soft_deletes_support() {
        let generator = DtoGenerator;
        let mut model = create_test_model();
        model.soft_deletes = true;

        let constructor_result = generator.generate_constructor_fields(&model).unwrap();
        let from_array_result = generator.generate_from_array_fields(&model).unwrap();
        let to_array_result = generator.generate_to_array_fields(&model).unwrap();

        assert!(constructor_result.contains("public ?string $deleted_at"));
        assert!(from_array_result.contains("$data['deleted_at']"));
        assert!(to_array_result.contains("'deleted_at' => $this->deleted_at"));
    }

    #[test]
    fn test_no_timestamps() {
        let generator = DtoGenerator;
        let mut model = create_test_model();
        model.timestamps = false;

        let constructor_result = generator.generate_constructor_fields(&model).unwrap();
        let from_array_result = generator.generate_from_array_fields(&model).unwrap();
        let to_array_result = generator.generate_to_array_fields(&model).unwrap();

        assert!(!constructor_result.contains("created_at"));
        assert!(!constructor_result.contains("updated_at"));
        assert!(!from_array_result.contains("created_at"));
        assert!(!from_array_result.contains("updated_at"));
        assert!(!to_array_result.contains("created_at"));
        assert!(!to_array_result.contains("updated_at"));
    }
}