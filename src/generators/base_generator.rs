use crate::error::{GeneratorError, Result};
use crate::types::{Config, ModelDefinition};
use crate::validation::Validator;
use crate::template::{TemplateContext, Template<PERSON><PERSON>er};

/// Base generator providing common functionality for all generators
pub struct BaseGenerator;

impl BaseGenerator {
    /// Validates inputs common to all generators
    pub fn validate_common_inputs(model: &ModelDefinition, config: &Config) -> Result<()> {
        Validator::validate_model(model)?;
        
        if config.output_dir.is_empty() {
            return Err(GeneratorError::Configuration(
                "Output directory cannot be empty".to_string()
            ));
        }
        
        Validator::validate_identifier(&model.name, "Model name")?;
        Ok(())
    }

    /// Creates a base template context with common variables
    pub fn create_base_context(model: &ModelDefinition, _config: &Config) -> TemplateContext {
        TemplateContext::new()
            .with("model_name", &model.name)
            .with("table_name", &model.table)
            .with("has_timestamps", model.timestamps.to_string())
            .with("has_soft_deletes", model.soft_deletes.to_string())
    }

    /// Renders a template with validation
    pub fn render_template_safe(
        template: &str,
        context: &TemplateContext,
        required_vars: &[&str]
    ) -> Result<String> {
        TemplateRenderer::render_with_required_vars(template, context, required_vars)
    }

    /// Ensures directory exists for file path
    pub fn ensure_directory_exists(file_path: &str) -> Result<()> {
        if let Some(parent) = std::path::Path::new(file_path).parent() {
            std::fs::create_dir_all(parent)
                .map_err(|e| GeneratorError::Io(e))?;
        }
        Ok(())
    }

    /// Formats PHP class imports
    pub fn format_php_imports(imports: &[&str]) -> String {
        if imports.is_empty() {
            return String::new();
        }
        
        let mut formatted = String::new();
        for import in imports {
            formatted.push_str(&format!("use {};\n", import));
        }
        formatted.push('\n');
        formatted
    }

    /// Formats PHP traits usage
    pub fn format_php_traits(traits: &[&str]) -> String {
        if traits.is_empty() {
            return String::new();
        }
        
        format!("    use {};\n\n", traits.join(", "))
    }

    /// Creates PHP class header
    pub fn create_php_class_header(
        namespace: &str,
        imports: &[&str],
        class_name: &str,
        extends: Option<&str>,
        implements: &[&str]
    ) -> String {
        let mut content = String::new();
        
        // PHP opening tag and namespace
        content.push_str("<?php\n\n");
        content.push_str(&format!("namespace {};\n\n", namespace));
        
        // Imports
        content.push_str(&Self::format_php_imports(imports));
        
        // Class declaration
        content.push_str(&format!("class {}", class_name));
        
        if let Some(parent) = extends {
            content.push_str(&format!(" extends {}", parent));
        }
        
        if !implements.is_empty() {
            content.push_str(&format!(" implements {}", implements.join(", ")));
        }
        
        content.push_str("\n{\n");
        content
    }
}

/// Trait for generators that can be configured
pub trait ConfigurableGenerator {
    type Config: Default + Clone;
    
    fn with_config(config: Self::Config) -> Self;
    fn get_config(&self) -> &Self::Config;
}

/// Trait for generators that support different output formats
pub trait MultiFormatGenerator {
    fn generate_json(&self, model: &ModelDefinition, config: &Config) -> Result<String>;
    fn generate_yaml(&self, model: &ModelDefinition, config: &Config) -> Result<String>;
}

/// Common generator configuration
#[derive(Debug, Clone, Default)]
pub struct GeneratorConfig {
    pub include_comments: bool,
    pub include_validation: bool,
    pub use_strict_types: bool,
    pub format_code: bool,
}

/// Generator factory for creating generators with common configuration
pub struct GeneratorFactory {
    config: GeneratorConfig,
}

impl GeneratorFactory {
    pub fn new(config: GeneratorConfig) -> Self {
        Self { config }
    }

    pub fn create_model_generator(&self) -> crate::generators::model_generator::ModelGenerator {
        crate::generators::model_generator::ModelGenerator
    }

    pub fn create_dto_generator(&self) -> crate::generators::dto_generator::DtoGenerator {
        crate::generators::dto_generator::DtoGenerator
    }

    // Add other generator creation methods...
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::types::{FillableGuarded, FieldType, Field};

    fn create_test_model() -> ModelDefinition {
        ModelDefinition {
            name: "TestModel".to_string(),
            table: "test_models".to_string(),
            fields: vec![
                Field {
                    name: "name".to_string(),
                    field_type: FieldType::String,
                    nullable: false,
                    unique: false,
                    default: None,
                    length: Some(255),
                    index: false,
                    enum_values: vec![],
                    decimal_precision: None,
                    unsigned: false,
                    auto_increment: false,
                    primary: false,
                    comment: None,
                    validation_rules: vec![],
                    cast_type: None,
                }
            ],
            timestamps: true,
            soft_deletes: false,
            relationships: vec![],
            pivot_tables: vec![],
            validation_rules: vec![],
            traits: vec![],
            fillable_guarded: FillableGuarded::All,
        }
    }

    fn create_test_config() -> Config {
        Config {
            models: vec![],
            output_dir: "/tmp/test".to_string(),
            namespace: "App\\Models".to_string(),
            generate_models: true,
            generate_controllers: true,
            generate_resources: true,
            generate_factories: true,
            generate_migrations: true,
            generate_pivot_tables: true,
            generate_validation_rules: true,
            generate_dto: true,
            use_ddd_structure: false,
            database_engine: "mysql".to_string(),
            force_overwrite: false,
        }
    }

    #[test]
    fn test_validate_common_inputs() {
        let model = create_test_model();
        let config = create_test_config();
        
        assert!(BaseGenerator::validate_common_inputs(&model, &config).is_ok());
    }

    #[test]
    fn test_create_base_context() {
        let model = create_test_model();
        let config = create_test_config();
        
        let context = BaseGenerator::create_base_context(&model, &config);
        
        assert!(context.contains("model_name"));
        assert!(context.contains("table_name"));
        assert!(context.contains("has_timestamps"));
        assert!(context.contains("has_soft_deletes"));
    }

    #[test]
    fn test_format_php_imports() {
        let imports = vec!["Illuminate\\Database\\Eloquent\\Model", "App\\Traits\\HasUuid"];
        let result = BaseGenerator::format_php_imports(&imports);
        
        assert!(result.contains("use Illuminate\\Database\\Eloquent\\Model;"));
        assert!(result.contains("use App\\Traits\\HasUuid;"));
    }

    #[test]
    fn test_format_php_traits() {
        let traits = vec!["HasFactory", "SoftDeletes"];
        let result = BaseGenerator::format_php_traits(&traits);
        
        assert_eq!(result, "    use HasFactory, SoftDeletes;\n\n");
    }

    #[test]
    fn test_create_php_class_header() {
        let result = BaseGenerator::create_php_class_header(
            "App\\Models",
            &["Illuminate\\Database\\Eloquent\\Model"],
            "User",
            Some("Model"),
            &[]
        );
        
        assert!(result.contains("<?php"));
        assert!(result.contains("namespace App\\Models;"));
        assert!(result.contains("use Illuminate\\Database\\Eloquent\\Model;"));
        assert!(result.contains("class User extends Model"));
    }
}
