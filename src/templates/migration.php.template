<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('{{table_name}}', function (Blueprint $table) {
            {{id_field}}
            {{fields}}
            {{timestamps}}
            {{soft_deletes}}
        });

        {{foreign_keys}}
    }

    public function down(): void
    {
        Schema::dropIfExists('{{table_name}}');
    }
};
