models:
  - name: User
    table: users
    timestamps: true
    fields:
      - name: name
        type: string
        length: 255
        nullable: false
      - name: email
        type: string
        length: 255
        nullable: false
        unique: true
      - name: age
        type: integer
        nullable: true
    relationships:
      - type: hasMany
        model: Post
        foreignKey: user_id

  - name: Post
    table: posts
    timestamps: true
    fields:
      - name: user_id
        type: bigInteger
        nullable: false
      - name: title
        type: string
        length: 255
        nullable: false
      - name: content
        type: text
        nullable: false
      - name: published
        type: boolean
        default: "false"
    relationships:
      - type: belongsTo
        model: User
        foreignKey: user_id
