<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use App\Models\Review;

class ReviewFactory extends Factory
{
    protected $model = Review::class;

    public function definition(): array
    {
        return [
            'user_id' => fake()->numberBetween(1, 100),
            'product_id' => fake()->numberBetween(1, 100),
            'rating' => fake()->numberBetween(0, 255),
            'title' => fake()->sentence(3),
            'comment' => fake()->text(),
            'is_verified' => fake()->boolean(),
        ];
    }
}
