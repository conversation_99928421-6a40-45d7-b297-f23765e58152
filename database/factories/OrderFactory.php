<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use App\Models\Order;

class OrderFactory extends Factory
{
    protected $model = Order::class;

    public function definition(): array
    {
        return [
            'user_id' => fake()->numberBetween(1, 100),
            'order_number' => fake()->word(),
            'status' => fake()->randomElement(['option1', 'option2', 'option3']),
            'subtotal' => fake()->randomFloat(2, 0, 1000),
            'tax_amount' => fake()->randomFloat(2, 0, 1000),
            'shipping_amount' => fake()->randomFloat(2, 0, 1000),
            'total_amount' => fake()->randomFloat(2, 0, 1000),
            'notes' => fake()->text(),
            'shipped_at' => fake()->dateTime(),
        ];
    }
}
