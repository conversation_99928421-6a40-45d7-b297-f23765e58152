<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use App\Models\Address;

class AddressFactory extends Factory
{
    protected $model = Address::class;

    public function definition(): array
    {
        return [
            'user_id' => fake()->numberBetween(1, 100),
            'type' => fake()->randomElement(['option1', 'option2', 'option3']),
            'first_name' => fake()->name(),
            'last_name' => fake()->name(),
            'company' => fake()->word(),
            'address_line_1' => fake()->address(),
            'address_line_2' => fake()->address(),
            'city' => fake()->city(),
            'state' => fake()->word(),
            'postal_code' => fake()->word(),
            'country' => fake()->country(),
            'is_default' => fake()->boolean(),
        ];
    }
}
