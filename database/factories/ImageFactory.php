<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use App\Models\Image;

class ImageFactory extends Factory
{
    protected $model = Image::class;

    public function definition(): array
    {
        return [
            'imageable_id' => fake()->numberBetween(1, 100),
            'imageable_type' => fake()->word(),
            'filename' => fake()->name(),
            'original_name' => fake()->name(),
            'mime_type' => fake()->word(),
            'size' => fake()->numberBetween(1, 100),
            'alt_text' => fake()->word(),
            'sort_order' => fake()->numberBetween(1, 100),
        ];
    }
}
