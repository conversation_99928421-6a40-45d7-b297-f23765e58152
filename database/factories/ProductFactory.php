<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use App\Models\Product;

class ProductFactory extends Factory
{
    protected $model = Product::class;

    public function definition(): array
    {
        return [
            'category_id' => fake()->numberBetween(1, 100),
            'name' => fake()->name(),
            'slug' => fake()->word(),
            'description' => fake()->paragraph(),
            'price' => fake()->randomFloat(2, 0, 1000),
            'compare_price' => fake()->randomFloat(2, 0, 1000),
            'sku' => fake()->word(),
            'stock_quantity' => fake()->numberBetween(1, 100),
            'status' => fake()->randomElement(['option1', 'option2', 'option3']),
            'weight' => fake()->randomFloat(2, 0, 1000),
            'dimensions' => fake()->words(3),
        ];
    }
}
