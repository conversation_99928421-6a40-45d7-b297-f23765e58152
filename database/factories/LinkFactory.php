<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use App\Models\Link;

class LinkFactory extends Factory
{
    protected $model = Link::class;

    public function definition(): array
    {
        return [
            'user_id' => fake()->numberBetween(1, 100),
            'category_id' => fake()->numberBetween(1, 100),
            'title' => fake()->sentence(3),
            'url' => fake()->url(),
            'is_active' => fake()->boolean(),
            'order' => fake()->numberBetween(1, 100),
        ];
    }
}
