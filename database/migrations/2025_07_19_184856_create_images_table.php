<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('images', function (Blueprint $table) {
            $table->id();

                        $table->bigInteger('imageable_id')->index();
            $table->string('imageable_type')->index();
            $table->string('filename', 255);
            $table->string('original_name', 255);
            $table->string('mime_type', 100);
            $table->integer('size');
            $table->string('alt_text', 255)->nullable();
            $table->integer('sort_order')->default('0');

            $table->timestamps();
            
        });

        
    }

    public function down(): void
    {
        Schema::dropIfExists('images');
    }
};
