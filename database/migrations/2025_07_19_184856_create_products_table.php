<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('products', function (Blueprint $table) {
            $table->id();

                        $table->bigInteger('category_id')->index();
            $table->string('name', 255);
            $table->string('slug', 255)->unique()->index();
            $table->longText('description')->nullable();
            $table->decimal('price');
            $table->decimal('compare_price')->nullable();
            $table->string('sku', 100)->unique();
            $table->integer('stock_quantity')->default('0');
            $table->enum('status')->default('active');
            $table->decimal('weight')->nullable();
            $table->json('dimensions')->nullable();

            $table->timestamps();
            
        });

        
    }

    public function down(): void
    {
        Schema::dropIfExists('products');
    }
};
