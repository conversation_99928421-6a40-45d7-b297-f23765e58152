<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('users', function (Blueprint $table) {
            $table->id();

                        $table->string('first_name', 50);
            $table->string('last_name', 50);
            $table->string('email', 255)->unique()->index();
            $table->string('phone', 20)->nullable();
            $table->date('date_of_birth')->nullable();

            $table->timestamps();
            
        });

        
    }

    public function down(): void
    {
        Schema::dropIfExists('users');
    }
};
