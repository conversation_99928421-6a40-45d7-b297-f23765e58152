<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('users', function (Blueprint $table) {
            $table->id();

                        $table->string('name', 100);
            $table->string('email', 255)->unique()->index();
            $table->string('password', 255);
            $table->text('bio')->nullable();
            $table->string('avatar_url', 255)->nullable();

            $table->timestamps();
            
        });

        
    }

    public function down(): void
    {
        Schema::dropIfExists('users');
    }
};
