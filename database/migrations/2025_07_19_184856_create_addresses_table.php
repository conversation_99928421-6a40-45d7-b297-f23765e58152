<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('addresses', function (Blueprint $table) {
            $table->id();

                        $table->bigInteger('user_id')->index();
            $table->enum('type');
            $table->string('first_name', 50);
            $table->string('last_name', 50);
            $table->string('company', 100)->nullable();
            $table->string('address_line_1', 255);
            $table->string('address_line_2', 255)->nullable();
            $table->string('city', 100);
            $table->string('state', 100);
            $table->string('postal_code', 20);
            $table->string('country', 2);
            $table->boolean('is_default')->default('false');

            $table->timestamps();
            
        });

        
    }

    public function down(): void
    {
        Schema::dropIfExists('addresses');
    }
};
