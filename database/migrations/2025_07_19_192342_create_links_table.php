<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('links', function (Blueprint $table) {
            $table->id();

                        $table->bigInteger('user_id')->index();
            $table->bigInteger('category_id')->nullable()->index();
            $table->string('title', 100);
            $table->string('url', 255);
            $table->boolean('is_active')->default('true');
            $table->integer('order')->nullable();

            $table->timestamps();
            
        });

        
    }

    public function down(): void
    {
        Schema::dropIfExists('links');
    }
};
