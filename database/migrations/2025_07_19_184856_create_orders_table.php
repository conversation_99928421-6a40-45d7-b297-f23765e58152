<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('orders', function (Blueprint $table) {
            $table->id();

                        $table->bigInteger('user_id')->index();
            $table->string('order_number', 50)->unique();
            $table->enum('status')->default('pending');
            $table->decimal('subtotal');
            $table->decimal('tax_amount')->default('0.00');
            $table->decimal('shipping_amount')->default('0.00');
            $table->decimal('total_amount');
            $table->text('notes')->nullable();
            $table->timestamp('shipped_at')->nullable();

            $table->timestamps();
            
        });

        
    }

    public function down(): void
    {
        Schema::dropIfExists('orders');
    }
};
