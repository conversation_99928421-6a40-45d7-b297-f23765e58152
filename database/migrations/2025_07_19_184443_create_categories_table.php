<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('categories', function (Blueprint $table) {
            $table->id();

                        $table->string('name', 100)->unique();
            $table->string('slug', 100)->unique()->index();
            $table->text('description')->nullable();
            $table->bigInteger('parent_id')->nullable()->index();
            $table->integer('sort_order')->default('0');

            $table->timestamps();
            
        });

        
    }

    public function down(): void
    {
        Schema::dropIfExists('categories');
    }
};
