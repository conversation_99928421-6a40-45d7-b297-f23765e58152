<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('order_items', function (Blueprint $table) {
            $table->id();

                        $table->bigInteger('order_id')->index();
            $table->bigInteger('product_id')->index();
            $table->integer('quantity');
            $table->decimal('unit_price');
            $table->decimal('total_price');

            $table->timestamps();
            
        });

        
    }

    public function down(): void
    {
        Schema::dropIfExists('order_items');
    }
};
