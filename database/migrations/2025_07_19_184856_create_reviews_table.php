<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('reviews', function (Blueprint $table) {
            $table->id();

                        $table->bigInteger('user_id')->index();
            $table->bigInteger('product_id')->index();
            $table->tinyInteger('rating');
            $table->string('title', 255)->nullable();
            $table->text('comment')->nullable();
            $table->boolean('is_verified')->default('false');

            $table->timestamps();
            
        });

        
    }

    public function down(): void
    {
        Schema::dropIfExists('reviews');
    }
};
