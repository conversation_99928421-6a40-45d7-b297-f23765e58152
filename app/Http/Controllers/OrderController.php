<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Order;
use App\Http\Resources\OrderResource;

class OrderController extends Controller
{
    public function index()
    {
        return OrderResource::collection(Order::all());
    }

    public function show(Order $order)
    {
        return new OrderResource($order); 
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'user_id' => 'required',
            'order_number' => 'required',
            'status' => 'required',
            'subtotal' => 'required',
            'tax_amount' => 'required',
            'shipping_amount' => 'required',
            'total_amount' => 'required',
            'notes' => 'nullable',
            'shipped_at' => 'nullable',
        ]);

        $order = Order::create($validated);
        return new OrderResource($order); 
    }

    public function update(Request $request, Order $order)
    {
        $validated = $request->validate([
            'user_id' => 'required',
            'order_number' => 'required',
            'status' => 'required',
            'subtotal' => 'required',
            'tax_amount' => 'required',
            'shipping_amount' => 'required',
            'total_amount' => 'required',
            'notes' => 'nullable',
            'shipped_at' => 'nullable',
        ]);

        $order->update($validated);
        return new OrderResource($order); 
    }

    public function destroy(Order $order)
    {
        $order->delete();
        return response()->json(null, 204);
    }
}
