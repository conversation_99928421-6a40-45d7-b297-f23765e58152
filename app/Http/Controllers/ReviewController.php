<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Review;
use App\Http\Resources\ReviewResource;

class ReviewController extends Controller
{
    public function index()
    {
        return ReviewResource::collection(Review::all());
    }

    public function show(Review $review)
    {
        return new ReviewResource($review); 
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'user_id' => 'required',
            'product_id' => 'required',
            'rating' => 'required',
            'title' => 'nullable',
            'comment' => 'nullable',
            'is_verified' => 'required',
        ]);

        $review = Review::create($validated);
        return new ReviewResource($review); 
    }

    public function update(Request $request, Review $review)
    {
        $validated = $request->validate([
            'user_id' => 'required',
            'product_id' => 'required',
            'rating' => 'required',
            'title' => 'nullable',
            'comment' => 'nullable',
            'is_verified' => 'required',
        ]);

        $review->update($validated);
        return new ReviewResource($review); 
    }

    public function destroy(Review $review)
    {
        $review->delete();
        return response()->json(null, 204);
    }
}
