<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Image;
use App\Http\Resources\ImageResource;

class ImageController extends Controller
{
    public function index()
    {
        return ImageResource::collection(Image::all());
    }

    public function show(Image $image)
    {
        return new ImageResource($image); 
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'imageable_id' => 'required',
            'imageable_type' => 'required',
            'filename' => 'required',
            'original_name' => 'required',
            'mime_type' => 'required',
            'size' => 'required',
            'alt_text' => 'nullable',
            'sort_order' => 'required',
        ]);

        $image = Image::create($validated);
        return new ImageResource($image); 
    }

    public function update(Request $request, Image $image)
    {
        $validated = $request->validate([
            'imageable_id' => 'required',
            'imageable_type' => 'required',
            'filename' => 'required',
            'original_name' => 'required',
            'mime_type' => 'required',
            'size' => 'required',
            'alt_text' => 'nullable',
            'sort_order' => 'required',
        ]);

        $image->update($validated);
        return new ImageResource($image); 
    }

    public function destroy(Image $image)
    {
        $image->delete();
        return response()->json(null, 204);
    }
}
