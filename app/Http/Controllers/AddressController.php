<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Address;
use App\Http\Resources\AddressResource;

class AddressController extends Controller
{
    public function index()
    {
        return AddressResource::collection(Address::all());
    }

    public function show(Address $address)
    {
        return new AddressResource($address); 
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'user_id' => 'required',
            'type' => 'required',
            'first_name' => 'required',
            'last_name' => 'required',
            'company' => 'nullable',
            'address_line_1' => 'required',
            'address_line_2' => 'nullable',
            'city' => 'required',
            'state' => 'required',
            'postal_code' => 'required',
            'country' => 'required',
            'is_default' => 'required',
        ]);

        $address = Address::create($validated);
        return new AddressResource($address); 
    }

    public function update(Request $request, Address $address)
    {
        $validated = $request->validate([
            'user_id' => 'required',
            'type' => 'required',
            'first_name' => 'required',
            'last_name' => 'required',
            'company' => 'nullable',
            'address_line_1' => 'required',
            'address_line_2' => 'nullable',
            'city' => 'required',
            'state' => 'required',
            'postal_code' => 'required',
            'country' => 'required',
            'is_default' => 'required',
        ]);

        $address->update($validated);
        return new AddressResource($address); 
    }

    public function destroy(Address $address)
    {
        $address->delete();
        return response()->json(null, 204);
    }
}
