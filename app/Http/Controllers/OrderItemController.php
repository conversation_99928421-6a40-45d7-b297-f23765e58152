<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\OrderItem;
use App\Http\Resources\OrderItemResource;

class OrderItemController extends Controller
{
    public function index()
    {
        return OrderItemResource::collection(OrderItem::all());
    }

    public function show(OrderItem $orderitem)
    {
        return new OrderItemResource($orderitem); 
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'order_id' => 'required',
            'product_id' => 'required',
            'quantity' => 'required',
            'unit_price' => 'required',
            'total_price' => 'required',
        ]);

        $orderitem = OrderItem::create($validated);
        return new OrderItemResource($orderitem); 
    }

    public function update(Request $request, OrderItem $orderitem)
    {
        $validated = $request->validate([
            'order_id' => 'required',
            'product_id' => 'required',
            'quantity' => 'required',
            'unit_price' => 'required',
            'total_price' => 'required',
        ]);

        $orderitem->update($validated);
        return new OrderItemResource($orderitem); 
    }

    public function destroy(OrderItem $orderitem)
    {
        $orderitem->delete();
        return response()->json(null, 204);
    }
}
