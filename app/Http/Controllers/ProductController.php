<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Product;
use App\Http\Resources\ProductResource;

class ProductController extends Controller
{
    public function index()
    {
        return ProductResource::collection(Product::all());
    }

    public function show(Product $product)
    {
        return new ProductResource($product); 
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'category_id' => 'required',
            'name' => 'required',
            'slug' => 'required',
            'description' => 'nullable',
            'price' => 'required',
            'compare_price' => 'nullable',
            'sku' => 'required',
            'stock_quantity' => 'required',
            'status' => 'required',
            'weight' => 'nullable',
            'dimensions' => 'nullable',
        ]);

        $product = Product::create($validated);
        return new ProductResource($product); 
    }

    public function update(Request $request, Product $product)
    {
        $validated = $request->validate([
            'category_id' => 'required',
            'name' => 'required',
            'slug' => 'required',
            'description' => 'nullable',
            'price' => 'required',
            'compare_price' => 'nullable',
            'sku' => 'required',
            'stock_quantity' => 'required',
            'status' => 'required',
            'weight' => 'nullable',
            'dimensions' => 'nullable',
        ]);

        $product->update($validated);
        return new ProductResource($product); 
    }

    public function destroy(Product $product)
    {
        $product->delete();
        return response()->json(null, 204);
    }
}
