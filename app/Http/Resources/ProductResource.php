<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ProductResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'category_id' => $this->category_id,
            'name' => $this->name,
            'slug' => $this->slug,
            'description' => $this->description,
            'price' => $this->price,
            'compare_price' => $this->compare_price,
            'sku' => $this->sku,
            'stock_quantity' => $this->stock_quantity,
            'status' => $this->status,
            'weight' => $this->weight,
            'dimensions' => $this->dimensions,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
