<?php

namespace App\DTOs;


class OrderDTO {

    public function __construct
    (
        public int $id,
        public int $user_id,
        public string $order_number,
        public string $status,
        public float $subtotal,
        public float $tax_amount,
        public float $shipping_amount,
        public float $total_amount,
        public ?string $notes,
        public ?string $shipped_at,
        public ?string $created_at,
        public ?string $updated_at
    )
    {
    }


     public static function fromArray(array $data): self
     {
            return new self(
                $data['id'],
                $data['user_id'],
                $data['order_number'],
                $data['status'],
                $data['subtotal'],
                $data['tax_amount'],
                $data['shipping_amount'],
                $data['total_amount'],
                $data['notes'],
                $data['shipped_at'],
                $data['created_at'],
                $data['updated_at']
            );
     }


    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'user_id' => $this->user_id,
            'order_number' => $this->order_number,
            'status' => $this->status,
            'subtotal' => $this->subtotal,
            'tax_amount' => $this->tax_amount,
            'shipping_amount' => $this->shipping_amount,
            'total_amount' => $this->total_amount,
            'notes' => $this->notes,
            'shipped_at' => $this->shipped_at,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at
        ];
    }

}