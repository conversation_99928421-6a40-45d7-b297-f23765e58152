<?php

namespace App\DTOs;


class OrderItemDTO {

    public function __construct
    (
        public int $id,
        public int $order_id,
        public int $product_id,
        public int $quantity,
        public float $unit_price,
        public float $total_price,
        public ?string $created_at,
        public ?string $updated_at
    )
    {
    }


     public static function fromArray(array $data): self
     {
            return new self(
                $data['id'],
                $data['order_id'],
                $data['product_id'],
                $data['quantity'],
                $data['unit_price'],
                $data['total_price'],
                $data['created_at'],
                $data['updated_at']
            );
     }


    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'order_id' => $this->order_id,
            'product_id' => $this->product_id,
            'quantity' => $this->quantity,
            'unit_price' => $this->unit_price,
            'total_price' => $this->total_price,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at
        ];
    }

}