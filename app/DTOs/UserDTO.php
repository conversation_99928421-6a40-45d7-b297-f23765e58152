<?php

namespace App\DTOs;


class UserDTO {

    public function __construct
    (
        public int $id,
        public string $name,
        public string $email,
        public string $password,
        public ?string $bio,
        public ?string $avatar_url,
        public ?string $created_at,
        public ?string $updated_at
    )
    {
    }


     public static function fromArray(array $data): self
     {
            return new self(
                $data['id'],
                $data['name'],
                $data['email'],
                $data['password'],
                $data['bio'],
                $data['avatar_url'],
                $data['created_at'],
                $data['updated_at']
            );
     }


    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'email' => $this->email,
            'password' => $this->password,
            'bio' => $this->bio,
            'avatar_url' => $this->avatar_url,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at
        ];
    }

}