<?php

namespace App\DTOs;


class ImageDTO {

    public function __construct
    (
        public int $id,
        public int $imageable_id,
        public string $imageable_type,
        public string $filename,
        public string $original_name,
        public string $mime_type,
        public int $size,
        public ?string $alt_text,
        public int $sort_order,
        public ?string $created_at,
        public ?string $updated_at
    )
    {
    }


     public static function fromArray(array $data): self
     {
            return new self(
                $data['id'],
                $data['imageable_id'],
                $data['imageable_type'],
                $data['filename'],
                $data['original_name'],
                $data['mime_type'],
                $data['size'],
                $data['alt_text'],
                $data['sort_order'],
                $data['created_at'],
                $data['updated_at']
            );
     }


    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'imageable_id' => $this->imageable_id,
            'imageable_type' => $this->imageable_type,
            'filename' => $this->filename,
            'original_name' => $this->original_name,
            'mime_type' => $this->mime_type,
            'size' => $this->size,
            'alt_text' => $this->alt_text,
            'sort_order' => $this->sort_order,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at
        ];
    }

}