<?php

namespace App\DTOs;


class AddressDTO {

    public function __construct
    (
        public int $id,
        public int $user_id,
        public string $type,
        public string $first_name,
        public string $last_name,
        public ?string $company,
        public string $address_line_1,
        public ?string $address_line_2,
        public string $city,
        public string $state,
        public string $postal_code,
        public string $country,
        public bool $is_default,
        public ?string $created_at,
        public ?string $updated_at
    )
    {
    }


     public static function fromArray(array $data): self
     {
            return new self(
                $data['id'],
                $data['user_id'],
                $data['type'],
                $data['first_name'],
                $data['last_name'],
                $data['company'],
                $data['address_line_1'],
                $data['address_line_2'],
                $data['city'],
                $data['state'],
                $data['postal_code'],
                $data['country'],
                $data['is_default'],
                $data['created_at'],
                $data['updated_at']
            );
     }


    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'user_id' => $this->user_id,
            'type' => $this->type,
            'first_name' => $this->first_name,
            'last_name' => $this->last_name,
            'company' => $this->company,
            'address_line_1' => $this->address_line_1,
            'address_line_2' => $this->address_line_2,
            'city' => $this->city,
            'state' => $this->state,
            'postal_code' => $this->postal_code,
            'country' => $this->country,
            'is_default' => $this->is_default,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at
        ];
    }

}