<?php

namespace App\DTOs;


class ProductDTO {

    public function __construct
    (
        public int $id,
        public int $category_id,
        public string $name,
        public string $slug,
        public ?string $description,
        public float $price,
        public ?float $compare_price,
        public string $sku,
        public int $stock_quantity,
        public string $status,
        public ?float $weight,
        public ?array $dimensions,
        public ?string $created_at,
        public ?string $updated_at
    )
    {
    }


     public static function fromArray(array $data): self
     {
            return new self(
                $data['id'],
                $data['category_id'],
                $data['name'],
                $data['slug'],
                $data['description'],
                $data['price'],
                $data['compare_price'],
                $data['sku'],
                $data['stock_quantity'],
                $data['status'],
                $data['weight'],
                $data['dimensions'],
                $data['created_at'],
                $data['updated_at']
            );
     }


    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'category_id' => $this->category_id,
            'name' => $this->name,
            'slug' => $this->slug,
            'description' => $this->description,
            'price' => $this->price,
            'compare_price' => $this->compare_price,
            'sku' => $this->sku,
            'stock_quantity' => $this->stock_quantity,
            'status' => $this->status,
            'weight' => $this->weight,
            'dimensions' => $this->dimensions,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at
        ];
    }

}