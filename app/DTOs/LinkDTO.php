<?php

namespace App\DTOs;


class LinkDTO {

    public function __construct
    (
        public int $id,
        public int $user_id,
        public ?int $category_id,
        public string $title,
        public string $url,
        public bool $is_active,
        public ?int $order,
        public ?string $created_at,
        public ?string $updated_at
    )
    {
    }


     public static function fromArray(array $data): self
     {
            return new self(
                $data['id'],
                $data['user_id'],
                $data['category_id'],
                $data['title'],
                $data['url'],
                $data['is_active'],
                $data['order'],
                $data['created_at'],
                $data['updated_at']
            );
     }


    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'user_id' => $this->user_id,
            'category_id' => $this->category_id,
            'title' => $this->title,
            'url' => $this->url,
            'is_active' => $this->is_active,
            'order' => $this->order,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at
        ];
    }

}