<?php

namespace App\DTOs;


class CategoryDTO {

    public function __construct
    (
        public int $id,
        public string $name,
        public string $slug,
        public ?string $description,
        public ?int $parent_id,
        public int $sort_order,
        public ?string $created_at,
        public ?string $updated_at
    )
    {
    }


     public static function fromArray(array $data): self
     {
            return new self(
                $data['id'],
                $data['name'],
                $data['slug'],
                $data['description'],
                $data['parent_id'],
                $data['sort_order'],
                $data['created_at'],
                $data['updated_at']
            );
     }


    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'slug' => $this->slug,
            'description' => $this->description,
            'parent_id' => $this->parent_id,
            'sort_order' => $this->sort_order,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at
        ];
    }

}