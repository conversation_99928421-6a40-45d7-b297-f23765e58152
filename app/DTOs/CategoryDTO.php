<?php

namespace App\DTOs;


class CategoryDTO {

    public function __construct
    (
        public int $id,
        public int $user_id,
        public string $name,
        public ?string $created_at,
        public ?string $updated_at
    )
    {
    }


     public static function fromArray(array $data): self
     {
            return new self(
                $data['id'],
                $data['user_id'],
                $data['name'],
                $data['created_at'],
                $data['updated_at']
            );
     }


    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'user_id' => $this->user_id,
            'name' => $this->name,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at
        ];
    }

}