<?php

namespace App\DTOs;


class ReviewDTO {

    public function __construct
    (
        public int $id,
        public int $user_id,
        public int $product_id,
        public int $rating,
        public ?string $title,
        public ?string $comment,
        public bool $is_verified,
        public ?string $created_at,
        public ?string $updated_at
    )
    {
    }


     public static function fromArray(array $data): self
     {
            return new self(
                $data['id'],
                $data['user_id'],
                $data['product_id'],
                $data['rating'],
                $data['title'],
                $data['comment'],
                $data['is_verified'],
                $data['created_at'],
                $data['updated_at']
            );
     }


    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'user_id' => $this->user_id,
            'product_id' => $this->product_id,
            'rating' => $this->rating,
            'title' => $this->title,
            'comment' => $this->comment,
            'is_verified' => $this->is_verified,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at
        ];
    }

}