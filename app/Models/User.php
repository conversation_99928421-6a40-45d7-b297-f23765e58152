<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class User extends Model
{
    use HasFactory;

    protected $table = 'users';

    protected $fillable = [
        'name',
        'email',
        'password',
        'bio',
        'avatar_url'
    ];

    public function links()
    {
        return $this->hasMany(Link::class);
    }

    public function categories()
    {
        return $this->hasMany(Category::class);
    }

}
