<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Link extends Model
{
    use HasFactory;

    protected $table = 'links';

    protected $fillable = [
        'user_id',
        'category_id',
        'title',
        'url',
        'is_active',
        'order'
    ];

    protected $casts = [
        'user_id' => 'integer',
        'category_id' => 'integer',
        'is_active' => 'boolean',
        'order' => 'integer',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function category()
    {
        return $this->belongsTo(Category::class);
    }

}
