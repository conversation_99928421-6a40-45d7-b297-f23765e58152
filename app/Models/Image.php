<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Image extends Model
{
    use HasFactory;

    protected $table = 'images';

    protected $fillable = [
        'imageable_id',
        'imageable_type',
        'filename',
        'original_name',
        'mime_type',
        'size',
        'alt_text',
        'sort_order'
    ];

    protected $casts = [
        'imageable_id' => 'integer',
        'size' => 'integer',
        'sort_order' => 'integer',
    ];

    public function morphable()
    {
        return $this->morphTo('morphable');
    }

}
